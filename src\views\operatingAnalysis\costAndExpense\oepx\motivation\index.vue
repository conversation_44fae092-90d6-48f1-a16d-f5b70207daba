<template>
  <div class="factor-analysis-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "FactorAnalysisChart",
  props: {},
  data() {
    return {
      myChart: null,
      // 瀑布图数据
      chartData: [
        { name: "去年同期", value: 22, type: "start" },
        { name: "海上人员费", value: 5, type: "positive" },
        { name: "直升机", value: 8, type: "positive" },
        { name: "供应船", value: 9, type: "positive" },
        { name: "油料", value: 8, type: "positive" },
        { name: "信息通讯气象", value: 2, type: "positive" },
        { name: "维修费用", value: 8, type: "positive" },
        { name: "油气水处理", value: 2, type: "positive" },
        { name: "油井作业费", value: 8, type: "positive" },
        { name: "物流港杂", value: 3, type: "positive" },
        { name: "油气生产研究", value: 8, type: "positive" },
        { name: "保险", value: 22, type: "positive" },
        { name: "健康安全环保", value: 1, type: "positive" },
        { name: "租赁费", value: 8, type: "positive" },
        { name: "其他", value: 6, type: "positive" },
        { name: "去年累计", value: 50, type: "end" }
      ],
      // 响应式配置
      chartWidth: 0,
      isSmallScreen: false
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);
      
      // 计算瀑布图的累积值和显示值
      const processedData = this.processWaterfallData();
      
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: this.getTopMargin(), // 动态调整顶部边距以适应斜角标签
          left: "3%",
          right: "4%",
          bottom: this.getBottomMarginForAngled(), // 调整底部边距以适应斜角标签
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: processedData.categories,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: this.getAngledLabelFontSize(),
            },
            interval: this.getAngledLabelInterval(),
            rotate: 60, // 设置为60度斜角显示
            margin: this.getAngledLabelMargin(),
            // 斜角标签的对齐方式
            verticalAlign: 'top',
            align: 'center',
            // 确保标签不会被截断
            overflow: 'none',
            // 标签格式化，处理过长文本
            formatter: this.formatAngledLabel,
          },
        },
        yAxis: {
          type: "value",
          name: "亿元",
          nameTextStyle: {
            color: "#ACC2E2",
            align: "right",
            padding: [0, 10, 0, 0],
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
            },
            formatter: "{value}",
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          // 隐藏的辅助系列，用于定位
          {
            name: "辅助",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "transparent",
            },
            emphasis: {
              itemStyle: {
                color: "transparent",
              },
            },
            data: processedData.assistData,
            tooltip: {
              show: false,
            },
          },
          // 显示的柱状图系列
          {
            name: "销售收入",
            type: "bar",
            stack: "总量",
            barWidth: "60%",
            itemStyle: {
              color: (params) => {
                // 根据数据类型设置颜色
                const item = this.chartData[params.dataIndex];
                if (item.type === "start") {
                  return "#248EFF"; // 起始值使用深蓝色
                } else if (item.type === "end") {
                  return "#248EFF"; // 结束值使用深蓝色
                } else {
                  return "#58CFFF"; // 增减因子使用浅蓝色
                }
              },
              borderRadius: [2, 2, 2, 2],
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 11,
              fontWeight: "bold",
              formatter: (params) => {
                const item = this.chartData[params.dataIndex];
                if (item.type === "start" || item.type === "end") {
                  return `${item.value}`;
                } else {
                  return item.value > 0 ? `+${item.value}` : `${item.value}`;
                }
              },
            },
            data: processedData.displayData,
          },
        ],
      };

      this.myChart.setOption(option);
    },

    // 处理瀑布图数据
    processWaterfallData() {
      const categories = [];
      const assistData = [];
      const displayData = [];
      let cumulative = 0;

      this.chartData.forEach((item, index) => {
        categories.push(item.name);

        if (item.type === "start") {
          // 起始值
          assistData.push(0);
          displayData.push(item.value);
          cumulative = item.value;
        } else if (item.type === "end") {
          // 结束值 - 计算最终累积值
          const finalValue = cumulative;
          assistData.push(0);
          displayData.push(finalValue);
          // 更新chartData中的最终值以保持一致性
          this.chartData[index].value = finalValue;
        } else {
          // 中间的增减值
          assistData.push(cumulative);
          displayData.push(item.value);
          cumulative += item.value;
        }
      });

      return {
        categories,
        assistData,
        displayData,
      };
    },

    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const originalData = this.chartData[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${originalData.name}</div>`;
      
      if (originalData.type === "start" || originalData.type === "end") {
        content += `<div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>金额: ${originalData.value} 亿元</span>
        </div>`;
      } else {
        const sign = originalData.value > 0 ? "+" : "";
        content += `<div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>影响: ${sign}${originalData.value} 亿元</span>
        </div>`;
      }
      
      return content;
    },

    // 响应式处理
    handleResize() {
      if (this.$refs.chartBox) {
        this.chartWidth = this.$refs.chartBox.offsetWidth;
        this.isSmallScreen = this.chartWidth < 800;

        // 重新渲染图表以应用新的配置
        if (this.myChart) {
          this.myChart.resize();
          // 延迟重新初始化以确保尺寸计算正确
          this.$nextTick(() => {
            this.initChart();
          });
        }
      }
    },

    // 获取斜角标签的字体大小
    getAngledLabelFontSize() {
      // 斜角显示时的字体大小，考虑可读性
      if (this.chartWidth < 600) return 10;
      if (this.chartWidth < 800) return 11;
      return 12;
    },

    // 获取斜角标签的显示间隔
    getAngledLabelInterval() {
      // 斜角显示时的间隔策略
      const dataLength = this.chartData.length;
      if (this.chartWidth < 480) {
        // 极小屏幕需要间隔显示
        return Math.ceil(dataLength / 6) - 1;
      } else if (this.chartWidth < 600) {
        // 小屏幕适度间隔
        return Math.ceil(dataLength / 8) - 1;
      }
      // 其他情况显示所有标签
      return 0;
    },

    // 获取斜角标签的边距
    getAngledLabelMargin() {
      // 斜角标签需要适中的边距
      if (this.chartWidth < 600) return 10;
      return 12;
    },

    // 获取顶部边距以适应斜角标签
    getTopMargin() {
      // 斜角标签需要更多顶部空间
      if (this.chartWidth < 600) {
        return "18%"; // 小屏幕需要更多顶部空间
      } else if (this.chartWidth < 800) {
        return "16%"; // 中等屏幕
      } else {
        return "14%"; // 大屏幕
      }
    },

    // 获取底部边距以适应斜角标签
    getBottomMarginForAngled() {
      // 根据最长标签文本和斜角显示计算所需空间
      const maxLabelLength = Math.max(...this.chartData.map(item => item.name.length));

      if (this.chartWidth < 600) {
        // 小屏幕：斜角显示需要适中的底部空间
        return maxLabelLength > 4 ? "20%" : "18%";
      } else if (this.chartWidth < 800) {
        // 中等屏幕
        return maxLabelLength > 4 ? "18%" : "15%";
      } else {
        // 大屏幕：斜角显示比垂直显示需要更少的底部空间
        return maxLabelLength > 4 ? "16%" : "12%";
      }
    },

    // 格式化斜角标签
    formatAngledLabel(value) {
      // 斜角显示时的文本处理
      if (value.length > 8 && this.isSmallScreen) {
        return value.substring(0, 6) + '...';
      } else if (value.length > 10) {
        // 对于非常长的文本，即使在大屏幕也需要截断
        return value.substring(0, 8) + '...';
      }
      return value;
    },
  },
};
</script>

<style lang="scss" scoped>
.factor-analysis-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    // 为垂直标签增加最小高度
    min-height: 280px;
    max-height: 400px;

    // 响应式高度调整 - 垂直标签需要更多底部空间
    @media (max-width: 768px) {
      min-height: 320px;
      max-height: 450px;
    }

    @media (max-width: 480px) {
      min-height: 350px;
      max-height: 480px;
    }
  }
}

// 确保图表容器有足够空间显示垂直标签
@media (max-width: 768px) {
  .factor-analysis-chart {
    .chart-box {
      // 为垂直标签预留额外空间
      padding-bottom: 30px;
    }
  }
}

@media (max-width: 480px) {
  .factor-analysis-chart {
    .chart-box {
      // 小屏幕需要更多空间
      padding-bottom: 40px;
    }
  }
}
</style>
