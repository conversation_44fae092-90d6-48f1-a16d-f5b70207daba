# X轴斜角标签显示方案

## 概述
已将图表的x轴标签从90度垂直显示改为60度斜角显示，在保持良好可读性的同时提供更美观的视觉效果。

## 实现的功能

### 1. 斜角标签核心配置
```javascript
axisLabel: {
  rotate: 60,              // 60度斜角旋转
  verticalAlign: 'top',    // 顶部对齐
  align: 'center',         // 居中对齐
  overflow: 'none',        // 不截断标签
}
```

### 2. 优化的布局配置
```javascript
grid: {
  top: this.getTopMargin(),           // 动态顶部边距：14%-18%
  bottom: this.getBottomMarginForAngled(), // 动态底部边距：12%-20%
}
```

### 3. 响应式字体和间隔
- **字体大小**: 10px-12px（根据屏幕宽度）
- **显示间隔**: 智能间隔显示，避免重叠
- **边距调整**: 10px-12px（适合斜角显示）

## 布局空间优化

### 顶部边距策略
```javascript
getTopMargin() {
  if (this.chartWidth < 600) return "18%";      // 小屏幕
  else if (this.chartWidth < 800) return "16%"; // 中屏幕  
  else return "14%";                             // 大屏幕
}
```

### 底部边距策略
```javascript
getBottomMarginForAngled() {
  const maxLabelLength = Math.max(...this.chartData.map(item => item.name.length));
  
  if (this.chartWidth < 600) {
    return maxLabelLength > 4 ? "20%" : "18%";   // 小屏幕
  } else if (this.chartWidth < 800) {
    return maxLabelLength > 4 ? "18%" : "15%";   // 中屏幕
  } else {
    return maxLabelLength > 4 ? "16%" : "12%";   // 大屏幕
  }
}
```

### 容器高度调整
- **基础高度**: 260px - 380px
- **中屏幕**: 300px - 420px  
- **小屏幕**: 320px - 450px

## 视觉效果对比

### 斜角标签 vs 垂直标签
| 特性 | 垂直标签(90°) | 斜角标签(60°) |
|------|---------------|---------------|
| 视觉美观 | 较为生硬 | ✅ 更加美观 |
| 可读性 | 需要转头阅读 | ✅ 更易阅读 |
| 空间利用 | 需要更多底部空间 | ✅ 空间利用更均衡 |
| 整体协调 | 较为突兀 | ✅ 与图表更协调 |
| 现代感 | 传统样式 | ✅ 更具现代感 |

### 斜角标签 vs 水平标签
| 特性 | 水平标签(0°) | 斜角标签(60°) |
|------|--------------|---------------|
| 重叠问题 | ❌ 容易重叠 | ✅ 无重叠问题 |
| 长文本支持 | ❌ 需要截断 | ✅ 支持更长文本 |
| 视觉层次 | 平淡 | ✅ 更有层次感 |
| 空间效率 | 水平空间紧张 | ✅ 空间利用合理 |

## 响应式适配策略

### 屏幕尺寸适配
```javascript
// 小屏幕 (< 600px)
{
  fontSize: 10,
  interval: Math.ceil(dataLength / 6) - 1,
  margin: 10,
  topMargin: "18%",
  bottomMargin: "18%-20%"
}

// 中屏幕 (600-800px)  
{
  fontSize: 11,
  interval: Math.ceil(dataLength / 8) - 1,
  margin: 12,
  topMargin: "16%",
  bottomMargin: "15%-18%"
}

// 大屏幕 (> 800px)
{
  fontSize: 12,
  interval: 0,
  margin: 12,
  topMargin: "14%", 
  bottomMargin: "12%-16%"
}
```

### 文本处理策略
```javascript
formatAngledLabel(value) {
  if (value.length > 8 && this.isSmallScreen) {
    return value.substring(0, 6) + '...';
  } else if (value.length > 10) {
    return value.substring(0, 8) + '...';
  }
  return value;
}
```

## 设计原理

### 60度角度选择理由
1. **视觉平衡**: 60度提供了垂直和水平之间的完美平衡
2. **可读性**: 比90度更容易阅读，比45度更稳定
3. **空间效率**: 既避免重叠又不占用过多空间
4. **美观性**: 创造动感和现代感的视觉效果

### 对齐方式优化
- **verticalAlign: 'top'**: 确保标签从顶部开始对齐
- **align: 'center'**: 标签相对于刻度线居中对齐
- 这种组合在60度角下提供最佳的视觉效果

## 使用建议

### 适用场景
- ✅ 需要平衡美观性和可读性
- ✅ 标签数量中等（8-16个）
- ✅ 标签长度适中（4-8个字符）
- ✅ 追求现代化的视觉设计

### 最佳实践
1. **容器高度**: 确保最小高度满足斜角标签显示需求
2. **响应式测试**: 在各种屏幕尺寸下测试显示效果
3. **文本长度**: 控制标签文本长度，避免过长
4. **颜色对比**: 确保标签颜色与背景有足够对比度

## 性能优化

### 渲染优化
- 使用防抖处理窗口大小变化
- 动态计算布局参数，避免固定值
- 智能间隔显示，减少渲染负担

### 内存管理
- 正确清理事件监听器
- 组件销毁时释放图表实例
- 避免内存泄漏

## 故障排除

### 常见问题
1. **标签被截断**: 增加底部或顶部边距
2. **标签重叠**: 调整间隔设置或字体大小
3. **显示不全**: 检查容器高度设置
4. **角度不理想**: 可调整rotate值（推荐45-75度）

### 调试技巧
- 使用浏览器开发者工具检查标签位置
- 在控制台查看计算的边距值
- 测试不同数据长度下的显示效果
- 验证响应式断点是否正确触发

## 自定义配置

### 调整角度
```javascript
rotate: 45,  // 45度更平缓
rotate: 75,  // 75度更陡峭
```

### 调整对齐
```javascript
// 左对齐
align: 'left',
verticalAlign: 'top',

// 右对齐
align: 'right', 
verticalAlign: 'top',
```

斜角标签方案在保持良好可读性的同时，提供了更加美观和现代的视觉效果，是水平标签和垂直标签之间的最佳平衡选择。
